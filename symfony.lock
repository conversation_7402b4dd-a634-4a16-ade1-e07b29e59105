{"symfony/console": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/flex": {"version": "2.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/framework-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "5a1497d539f691b96afd45ae397ce5fe30beb4b9"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php", ".editorconfig"]}, "symfony/routing": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/twig-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/webpack-encore-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.0", "ref": "9ef5412a4a2a8415aca3a3f2b4edd3866aab9a19"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}}