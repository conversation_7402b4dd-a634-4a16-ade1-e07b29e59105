{"version": 3, "file": "cacheWrapper.js", "names": ["cacheWrapper", "cache", "key", "fn", "cached", "get", "undefined", "result", "set", "cacheWrapperSync"], "sources": ["../src/cacheWrapper.ts"], "sourcesContent": ["import { <PERSON><PERSON>, CosmiconfigResult } from './types';\n\nasync function cacheWrapper(\n  cache: Cache,\n  key: string,\n  fn: () => Promise<CosmiconfigResult>,\n): Promise<CosmiconfigResult> {\n  const cached = cache.get(key);\n  if (cached !== undefined) {\n    return cached;\n  }\n\n  const result = await fn();\n  cache.set(key, result);\n  return result;\n}\n\nfunction cacheWrapperSync(\n  cache: Cache,\n  key: string,\n  fn: () => CosmiconfigResult,\n): CosmiconfigResult {\n  const cached = cache.get(key);\n  if (cached !== undefined) {\n    return cached;\n  }\n\n  const result = fn();\n  cache.set(key, result);\n  return result;\n}\n\nexport { cacheWrapper, cacheWrapperSync };\n"], "mappings": ";;;;;;;;AAEA,eAAeA,YAAf,CACEC,KADF,EAEEC,GAFF,EAGEC,EAHF,EAI8B;EAC5B,MAAMC,MAAM,GAAGH,KAAK,CAACI,GAAN,CAAUH,GAAV,CAAf;;EACA,IAAIE,MAAM,KAAKE,SAAf,EAA0B;IACxB,OAAOF,MAAP;EACD;;EAED,MAAMG,MAAM,GAAG,MAAMJ,EAAE,EAAvB;EACAF,KAAK,CAACO,GAAN,CAAUN,GAAV,EAAeK,MAAf;EACA,OAAOA,MAAP;AACD;;AAED,SAASE,gBAAT,CACER,KADF,EAEEC,GAFF,EAGEC,EAHF,EAIqB;EACnB,MAAMC,MAAM,GAAGH,KAAK,CAACI,GAAN,CAAUH,GAAV,CAAf;;EACA,IAAIE,MAAM,KAAKE,SAAf,EAA0B;IACxB,OAAOF,MAAP;EACD;;EAED,MAAMG,MAAM,GAAGJ,EAAE,EAAjB;EACAF,KAAK,CAACO,GAAN,CAAUN,GAAV,EAAeK,MAAf;EACA,OAAOA,MAAP;AACD"}