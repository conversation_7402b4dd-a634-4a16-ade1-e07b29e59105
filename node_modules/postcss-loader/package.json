{"name": "postcss-loader", "version": "8.1.1", "description": "PostCSS loader for webpack", "license": "MIT", "repository": "webpack-contrib/postcss-loader", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/webpack-contrib/postcss-loader", "bugs": "https://github.com/webpack-contrib/postcss-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 18.12.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --cache --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"@rspack/core": "0.x || 1.x", "postcss": "^7.0.0 || ^8.0.1", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}, "dependencies": {"cosmiconfig": "^9.0.0", "jiti": "^1.20.0", "semver": "^7.5.4"}, "devDependencies": {"@babel/cli": "^7.23.9", "@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "cspell": "^8.4.1", "cssnano": "^6.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "jest": "^29.7.0", "less": "^4.2.0", "less-loader": "^12.2.0", "lint-staged": "^15.2.2", "memfs": "^4.7.6", "midas": "^2.0.3", "npm-run-all": "^4.1.5", "postcss": "^8.4.35", "postcss-dark-theme-class": "^1.2.1", "postcss-import": "^16.0.1", "postcss-js": "^4.0.1", "postcss-load-config": "^5.0.3", "postcss-nested": "^6.0.1", "postcss-short": "^5.0.0", "prettier": "^3.2.5", "sass": "^1.71.0", "sass-loader": "^14.1.1", "standard-version": "^9.3.2", "strip-ansi": "^7.1.0", "sugarss": "^4.0.1", "ts-jest": "^29.1.2", "ts-node": "^10.9.1", "webpack": "^5.90.3"}, "keywords": ["css", "postcss", "postcss-runner", "webpack", "webpack-loader"]}