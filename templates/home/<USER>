{% extends 'base.html.twig' %}

{% block title %}Tailwind CSS Test{% endblock %}

{% block body %}
<div class="min-h-screen flex items-center justify-center">
    <div class="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl">
        <div class="md:flex">
            <div class="p-8">
                <div class="uppercase tracking-wide text-sm text-indigo-500 font-semibold">
                    Tailwind CSS
                </div>
                <h1 class="block mt-1 text-lg leading-tight font-medium text-black">
                    Successfully Installed!
                </h1>
                <p class="mt-2 text-gray-500">
                    Tailwind CSS is now configured and ready to use in your Symfony application.
                    This card demonstrates various Tailwind utility classes working correctly.
                </p>
                <div class="mt-4">
                    <button class="btn-primary">
                        Test Button (Custom Component)
                    </button>
                    <button class="ml-2 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Another Button
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
