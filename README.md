# SailingHR - Symfony with Tailwind CSS

This Symfony application is configured with Tailwind CSS as the default CSS framework.

## Setup

### Prerequisites
- PHP 8.2+
- Node.js and npm
- Symfony CLI (optional but recommended)

### Installation
1. Install PHP dependencies:
   ```bash
   composer install
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Build assets:
   ```bash
   npm run build
   ```

## Development

### Asset Compilation
- **Development build**: `npm run dev`
- **Watch for changes**: `npm run watch`
- **Production build**: `npm run build`
- **Development server with hot reload**: `npm run dev-server`

### Running the Application
Start the Symfony development server:
```bash
symfony server:start
```

Or use PHP's built-in server:
```bash
php -S localhost:8000 -t public/
```

## Tailwind CSS Configuration

### Files
- `tailwind.config.js` - Tailwind configuration
- `postcss.config.js` - PostCSS configuration
- `assets/styles/app.css` - Main CSS file with Tailwind directives
- `webpack.config.js` - Webpack Encore configuration

### Content Sources
Tailwind is configured to scan the following files for classes:
- `./assets/**/*.js`
- `./templates/**/*.html.twig`
- `./src/**/*.php`

### Usage
Use Tailwind utility classes directly in your Twig templates:

```html
<div class="bg-blue-500 text-white p-4 rounded-lg">
    <h1 class="text-2xl font-bold">Hello Tailwind!</h1>
</div>
```

## Project Structure
```
├── assets/
│   ├── app.js              # Main JavaScript entry point
│   └── styles/
│       └── app.css         # Main CSS file with Tailwind directives
├── public/
│   └── build/              # Compiled assets (generated)
├── src/
│   └── Controller/         # Symfony controllers
├── templates/              # Twig templates
├── tailwind.config.js      # Tailwind configuration
├── postcss.config.js       # PostCSS configuration
└── webpack.config.js       # Webpack Encore configuration
```
